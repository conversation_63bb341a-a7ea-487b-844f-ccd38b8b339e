/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import {
  GlobalSearchParamsI,
  GlobalSearchCombinedResponseI,
} from './types';

export const forumGlobalSearchAPI = async (
  query: GlobalSearchParamsI,
): Promise<GlobalSearchCombinedResponseI> => {
  const result = await apiCall<unknown, GlobalSearchCombinedResponseI>(
    '/backend/api/v1/forum/global-search',
    'GET',
    {
      isAuth: true,
      query,
    },
  );
  return result;
};
