/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { IdNameI, ObjUnknownI } from '@/src/types/common/data';

interface DateRangeI {
  fromDate: string;
  untilDate: string;
}

export interface CheckUsernameQueryI extends ObjUnknownI {
  username: string;
}

export interface UpdateUsernameBodyI {
  username: string;
}

export interface UpdateUsernameResultI {
  profileId: string;
  username: string;
  isUsernameSaved: boolean;
}

export interface FetchProfileResultI {
  email: string;
  name: string;
  username: string;
  avatar: string | null;
  profileId: string;
  designation: SearchResultI | null;
  entity: SearchResultI | null;
}

export interface FetchUserProfileValueAddedCertificationsBodyI {
  type: 'VALUE_ADDED';
  pageSize: number;
}

export interface FetchUserProfileValueAddedCertificationsResultI extends DateRangeI {
  id: string;
  entity: IdNameI;
  certificateCourse: IdNameI;
}

export interface FetchUserProfileVisaDocumentationsBodyI {
  pageSize: number;
}

export interface FetchUserProfileVisaDocumentationsResultI extends DateRangeI {
  id: string;
  fileUrl: string;
  country: {
    name: string;
  };
  expiryStatus: 'EXPIRED' | 'EXPIRES_SOON' | 'VALID';
}

export interface FetchUserProfileSkillsResultI {
  id: string;
  name: string;
  category: string;
  dataType: string;
}

export interface EditUserProfilePayloadI {
  name?: string;
  description?: string;
  designation?: Omit<SearchResultI, 'name'>;
  entity?: Omit<SearchResultI, 'name'>;
  avatar: string | null;
}

export interface AcceptPrivacyPolicyResultI {
  profileId: string;
  isPrivacyPolicyAccepted: boolean;
}

export interface FetchPrivacyPolicyBodyI {
  type: string;
}

export interface FetchPrivacyPolicyResultI {
  id: string;
  content: string;
}

export type EditDeleteAvatarStorageQueryI = {
  fileUrl: string;
};
