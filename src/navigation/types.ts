/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { CompositeNavigationProp, NavigatorScreenParams } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import {
  FieldTypeI,
  ShipCreateEditPayloadI,
  ShipPreFilledDataTypeI,
} from '../screens/EditShipItem/components/EditShipItem/types';

export type ConnectionTypeI =
  | 'connections'
  | 'followers'
  | 'following'
  | 'mutuals'
  | 'requests_received'
  | 'requests_sent';

export type ContentType = 'USER_POST' | 'SCRAPBOOK_POST';

type PostIdParam = { postId: string };

export type AuthStackParamListI = {
  AddUserDetailScreen: undefined;
  CreateAccount: undefined;
  CreateAccountSuccess: undefined;
  VerifyEmail: { email: string; profileId: string };
  Onboarding: undefined;
  SearchScreen: {
    title: string;
    placeholder: string;
    selectionKey: string;
  };
  SetUsername: { email: string };
  UserLogin: undefined;
  ForgotPassword: undefined;
  VerifyPasswordReset: { email: string };
  ResetPassword: { email: string };
  PrivacyPolicy: { options?: boolean; email?: string };
  PolicyAcceptance: undefined;
};

export type HomeStackParamListI = {
  Comment: PostIdParam & { type?: ContentType; portUnLocode?: string };
  Connection: { profileId: string; type: ConnectionTypeI; isUserProfile?: boolean };
  GlobalSearch: undefined;
  Home: undefined;
  AIChat: undefined;
  Likes: PostIdParam & { type?: ContentType };
  EditExperienceList: { profileId?: string; editable?: boolean };
  EditSkillsList: { profileId?: string; category?: string };
  NotFound: undefined;
  OtherUserProfile: {
    profileId?: string;
    fromTabPress?: boolean;
  };
  PortProfile: { unLocode: string; dataType: string };
  Chats: { profileId: string };
  Chat: { id: string };
  ShipProfile: { imo: string; dataType: string };
  Votes: { type: 'upvote' | 'downvote'; id: string };
  EditEducationList: { profileId?: string; editable?: boolean };
  EditCertificationList: {
    profileId?: string;
    editable?: boolean;
    tab?: 'statutory' | 'valueAdded';
  };
  EditShipItem: {
    shipId?: string;
    data?: ShipPreFilledDataTypeI[];
    field?: FieldTypeI;
    entityId?: string;
    fromProfileExperience?: {
      id: string;
      imo: string;
    };
    refetch?: () => void;
  };
  EditDocumentList: { profileId?: string; editable?: boolean; tab?: 'identity' | 'visa' };
};

export type ProfileStackParamsListI = {
  BlockedUserProfiles: undefined;
  Terms: undefined;
  Privacy: undefined;
  Comment: PostIdParam & { type?: ContentType; portUnLocode?: string };
  Connection: { profileId: string; type: ConnectionTypeI; isUserProfile?: boolean };
  Chat: { id: string };
  EditCargoItem: {
    profileId: string;
    experienceId?: string;
    shipId?: string;
    cargoId?: string;
    data: ShipCreateEditPayloadI[];
    refetch: () => void;
    shipData: { fromDate: string; toDate: string | null };
  };
  EditCertificationItem: { profileId?: string; certificationId?: string };
  EditCertificationList: {
    profileId?: string;
    editable?: boolean;
    tab?: 'statutory' | 'valueAdded';
  };
  EditDocumentItem: { profileId?: string; documentId?: string; type?: string };
  EditDocumentList: { profileId?: string; editable?: boolean; tab?: 'identity' | 'visa' };
  EditEducationItem: { profileId?: string; educationId?: string };
  EditEducationList: { profileId?: string; editable?: boolean };
  EditEquipmentItem: {
    profileId: string;
    experienceId?: string;
    shipId?: string;
    equipmentId?: string;
    data: ShipCreateEditPayloadI[];
    refetch: () => void;
  };
  EditExperienceItem: { profileId?: string; experienceId?: string };
  EditExperienceList: { profileId?: string; editable?: boolean };
  EditSkillsList: { profileId?: string; category?: string };
  EditUserProfile: undefined;
  Likes: PostIdParam & { type?: ContentType };
  OtherUserProfile: {
    profileId?: string;
    fromTabPress?: boolean;
  };
  PortsVisited: { profileId?: string };
  SearchScreen: {
    title: string;
    placeholder: string;
    selectionKey: string;
    multipleSelection?: boolean;
    searchWithoutInput?: boolean;
  };
  EditDetail: { type: string; action: 'add' | 'contribute'; text?: string };
  UserProfile: {
    profileId?: string;
    fromTabPress?: boolean;
  };
  EditShipItem: {
    shipId?: string;
    data?: ShipPreFilledDataTypeI[];
    field?: FieldTypeI;
    entityId?: string;
    fromProfileExperience?: {
      id: string;
      imo: string;
    };
    refetch?: () => void;
  };
  ShipProfile: { imo: string; dataType: string };
  UserSettings: undefined;
};

export type CreateStackParamsListI = {
  CreateContent: {
    type?: ContentType;
    portUnLocode?: string;
    editing?: boolean;
    postId?: string;
  };
};

export type NotificationStackParamsListI = {
  Notification: undefined;
};

export type LearnCollabStackParamsListI = {
  Forum: undefined;
  ForumAnswers: { postId: string };
  ForumComments: { postId: string; type: 'FORUM_QUESTION' | 'FORUM_ANSWER' };
  ForumSearch: undefined;
  ForumFilter: undefined;
  CreateCommunity: undefined;
  CommunityRestrictions: { id: string };
  People: undefined;
  CommunityQuestion: { id: string };
  CreateQuestion: undefined;
  ForumSetting: undefined;
  CommunityMembers: { forumId?: string };
  CommunityBlocked: { forumId?: string };
  CommunityRequests: { forumId?: string };
  Votes: { type: 'upvote' | 'downvote'; id: string };
  Community: { id: string };
  MyCommunities: undefined;
  Explore: undefined;
  ExploreQna: undefined;
  ExploreTroubleShoot: undefined;
  SearchScreen: {
    title: string;
    placeholder: string;
    selectionKey: string;
    multipleSelection?: boolean;
    searchWithoutInput?: boolean;
  };
};

export type BottomTabParamListI = {
  CreateStack: NavigatorScreenParams<CreateStackParamsListI>;
  HomeStack: NavigatorScreenParams<HomeStackParamListI>;
  NotificationStack: NavigatorScreenParams<NotificationStackParamsListI>;
  ProfileStack: NavigatorScreenParams<ProfileStackParamsListI>;
  LearnCollabStack: NavigatorScreenParams<LearnCollabStackParamsListI>;
};

export type AppStackParamListI = AuthStackParamListI &
  ProfileStackParamsListI &
  HomeStackParamListI &
  NotificationStackParamsListI &
  CreateStackParamsListI;

export type RootStackParamListI = {
  auth: undefined;
  main: undefined;
  splash: undefined;
};

export type BottomTabNavigationI = CompositeNavigationProp<
  BottomTabNavigationProp<BottomTabParamListI>,
  StackNavigationProp<AppStackParamListI>
>;

export type StackScreenI<T> = {
  name: keyof T;
  component: React.FC;
};

export interface TabIconProps {
  IconComponent: React.FC<{ fill?: string; stroke?: string }>;
  focused: boolean;
  tabName: string;
}

export interface CreateButtonProps {
  focused: boolean;
}

export interface TabBarIconProps {
  focused: boolean;
  color?: string;
  size?: number;
}

export type HomeScreenActionsRef = React.RefObject<{
  scrollToTop?: () => void;
  handleRefresh?: () => void;
}>;
