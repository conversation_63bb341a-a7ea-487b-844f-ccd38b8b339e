/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { Text, Linking } from 'react-native';

interface Match {
  type: 'url' | 'hashtag' | 'highlight';
  text: string;
  index?: number;
  length: number;
}

export const renderTextWithHighlight = (
  text: string | null | undefined,
  highlightText?: string,
): React.ReactNode => {
  if (!text) return null;
  const trimmedText = text.trim();

  const urlRegex = /(https?:\/\/[^\s]+)/g;
  const hashtagRegex = /(#[a-zA-Z0-9_]+\b)/g;

  const combinedMatches: Match[] = [];

  // Add URL matches
  const urlMatches = [...trimmedText.matchAll(urlRegex)];
  urlMatches.forEach((match) => {
    combinedMatches.push({
      type: 'url',
      text: match[0],
      index: match.index,
      length: match[0]?.length,
    });
  });

  // Add hashtag matches
  const hashtagMatches = [...trimmedText.matchAll(hashtagRegex)];
  hashtagMatches.forEach((match) => {
    combinedMatches.push({
      type: 'hashtag',
      text: match[0],
      index: match.index,
      length: match[0]?.length,
    });
  });

  // Add highlight matches if highlightText is provided
  if (highlightText && highlightText.trim()) {
    const highlightRegex = new RegExp(
      highlightText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
      'gi',
    );
    const highlightMatches = [...trimmedText.matchAll(highlightRegex)];
    highlightMatches.forEach((match) => {
      // Only add if it doesn't overlap with existing matches
      const start = match.index || 0;
      const end = start + match[0].length;
      const hasOverlap = combinedMatches.some((existing) => {
        const existingStart = existing.index || 0;
        const existingEnd = existingStart + existing.length;
        return (start < existingEnd && end > existingStart);
      });
      
      if (!hasOverlap) {
        combinedMatches.push({
          type: 'highlight',
          text: match[0],
          index: match.index,
          length: match[0]?.length,
        });
      }
    });
  }

  // Sort matches by index
  combinedMatches.sort((a, b) => (a.index || 0) - (b.index || 0));

  const result: React.ReactNode[] = [];
  let lastIndex = 0;

  combinedMatches.forEach((match, index) => {
    const start = match.index || 0;

    // Add text before the match
    if (lastIndex < start) {
      const textSegment = trimmedText.substring(lastIndex, start);
      result.push(<Text key={`text-${index}`}>{textSegment}</Text>);
    }

    // Add the match with appropriate styling
    if (match.type === 'url') {
      result.push(
        <Text
          key={`url-${index}`}
          className="text-green-800 underline"
          onPress={() => Linking.openURL(match.text)}
        >
          {match.text}
        </Text>,
      );
    } else if (match.type === 'hashtag') {
      result.push(
        <Text key={`hashtag-${index}`} className="text-green-800" onPress={() => {}}>
          {match.text}
        </Text>,
      );
    } else if (match.type === 'highlight') {
      result.push(
        <Text key={`highlight-${index}`} className="bg-yellow-200 text-black">
          {match.text}
        </Text>,
      );
    }

    lastIndex = start + match.length;
  });

  // Add remaining text
  if (lastIndex < trimmedText.length) {
    const remainingText = trimmedText.substring(lastIndex);
    result.push(<Text key="remaining">{remainingText}</Text>);
  }

  return result.length > 0 ? result : <Text>{trimmedText}</Text>;
};
