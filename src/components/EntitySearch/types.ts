/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { IdTitleI } from '@/src/types/common/data';

export interface EntitySearchProps {
  title: string;
  placeholder: string;
  data?: IdTitleI | string;
  selectionKey: string;
  className?: string;
  titleClassName?: string;
  inputClassName?: string;
  multipleSelection?: boolean;
  error?: string;
  editable?: boolean;
  searchWithoutInput?: boolean;
  deletable?: boolean;
  onDelete?: () => void;
}
