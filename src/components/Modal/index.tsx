import React, { useState, useEffect } from 'react';
import {
  Pressable,
  Text,
  View,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  AppState,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import Modal from 'react-native-modal';
import { CustomModalProps } from './types';

const CustomModal: React.FC<CustomModalProps> = ({
  isVisible,
  title,
  description,
  cancelText = 'Cancel',
  confirmText = 'Delete',
  onConfirm,
  onCancel,
  inputPlaceholder,
  inputValue: externalInputValue,
  onInputChange,
  inputType,
  inputRequired = false,
  inputLabel,
  bodyComponent,
  isConfirming,
  confirmButtonVariant = 'default',
  cancelBtnClassName,
  confirmBtnClassName,
  cancelButtonMode = 'outlined',
  confirmButtonMode,
  disableConfirm,
}) => {
  const [inputValue, setInputValue] = useState(externalInputValue || '');
  const [shouldShowModal, setShouldShowModal] = useState(false);
  const isInputEnabled = inputType !== undefined;
  const isConfirmDisabled =
    (isInputEnabled && inputRequired && !inputValue.trim()) || disableConfirm;

  useFocusEffect(
    React.useCallback(() => {
      setShouldShowModal(isVisible);
      return () => {
        setShouldShowModal(false);
      };
    }, [isVisible]),
  );

  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background' || nextAppState === 'inactive') {
        setShouldShowModal(false);
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, []);

  useEffect(() => {
    setShouldShowModal(isVisible);
  }, [isVisible]);

  useEffect(() => {
    if (externalInputValue !== undefined) {
      setInputValue(externalInputValue);
    }
  }, [externalInputValue]);

  const handleInputChange = (text: string) => {
    setInputValue(text);
    onInputChange?.(text);
  };

  const handleConfirm = () => {
    if (!isConfirmDisabled) {
      onConfirm(inputValue);
    }
  };

  const handleCancel = () => {
    setShouldShowModal(false);
    onCancel();
  };

  const handleBackdropPress = () => {
    setShouldShowModal(false);
    onCancel();
  };

  const getButtonStyle = (mode: 'default' | 'danger' | 'outlined', isDisabled: boolean = false) => {
    const baseStyle = 'flex-1 py-3 rounded-xl justify-center items-center';

    if (isDisabled) {
      return `${baseStyle} bg-gray-400 border border-gray-400`;
    }

    switch (mode) {
      case 'danger':
        return `${baseStyle} bg-red-600 border border-red-600`;
      case 'default':
        return `${baseStyle} bg-[#5a8d3b] border border-[#5a8d3b]`;
      case 'outlined':
        return `${baseStyle} bg-white border border-gray-300`;
      default:
        return `${baseStyle} bg-white border border-gray-300`;
    }
  };

  const getTextColor = (mode: 'default' | 'danger' | 'outlined', isDisabled: boolean = false) => {
    if (isDisabled) {
      return 'text-white';
    }

    switch (mode) {
      case 'danger':
        return 'text-white';
      case 'default':
        return 'text-white';
      case 'outlined':
        return 'text-black';
      default:
        return 'text-black';
    }
  };

  const getConfirmButtonMode = () => {
    if (confirmButtonMode) {
      return confirmButtonMode;
    }
    return confirmButtonVariant === 'danger' ? 'danger' : 'default';
  };

  const finalConfirmMode = getConfirmButtonMode();
  const finalCancelMode = cancelButtonMode;

  return (
    <View>
      <Modal
        isVisible={shouldShowModal}
        onBackdropPress={handleBackdropPress}
        onBackButtonPress={handleCancel}
        animationIn="fadeIn"
        animationOut="fadeOut"
        animationInTiming={250}
        animationOutTiming={250}
        backdropTransitionInTiming={250}
        backdropTransitionOutTiming={1}
        backdropOpacity={0.5}
        statusBarTranslucent
        useNativeDriverForBackdrop
        hideModalContentWhileAnimating={false}
        avoidKeyboard
      >
        <KeyboardAvoidingView className='bg-white' behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
          <View className="w-full bg-white rounded-3xl p-6">
            <Text className="text-xl font-normal text-black mb-4">{title}</Text>
            {description && <Text className="text-sm text-black mb-4">{description}</Text>}

            {isInputEnabled && (
              <View className="mb-6">
                {inputLabel && (
                  <Text className="text-sm font-medium text-gray-700 mb-2">{inputLabel}</Text>
                )}
                <TextInput
                  className="border border-gray-300 rounded-xl px-4 py-3 text-black"
                  placeholder={inputPlaceholder}
                  value={inputValue}
                  onChangeText={handleInputChange}
                  keyboardType={inputType === 'number' ? 'numeric' : 'default'}
                  secureTextEntry={inputType === 'password'}
                  autoCapitalize={inputType === 'email' ? 'none' : 'sentences'}
                />
                {inputRequired && !inputValue.trim() && (
                  <Text className="text-red-500 text-xs mt-1">This field is required</Text>
                )}
              </View>
            )}

            {bodyComponent}

            <View className="flex-row gap-4">
              <Pressable
                className={cancelBtnClassName ?? getButtonStyle(finalCancelMode)}
                onPress={handleCancel}
              >
                <Text className={`${getTextColor(finalCancelMode)} text-base`}>{cancelText}</Text>
              </Pressable>

              <Pressable
                className={
                  confirmBtnClassName ??
                  `${getButtonStyle(finalConfirmMode, isConfirmDisabled)} flex-row gap-2`
                }
                onPress={handleConfirm}
                disabled={isConfirmDisabled || isConfirming}
              >
                {isConfirming && <ActivityIndicator size="small" color="#ffffff" />}
                <Text className={`${getTextColor(finalConfirmMode, isConfirmDisabled)} text-base`}>
                  {confirmText}
                </Text>
              </Pressable>
            </View>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    </View>
  );
};

export default CustomModal;
