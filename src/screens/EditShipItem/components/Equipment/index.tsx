import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import CustomModal from '@/src/components/Modal';
import AddItem from '@/src/assets/svgs/AddItem';
import EditPencil from '@/src/assets/svgs/EditPencil';
import DeleteIcon from '@/src/assets/svgs/TrashBin';
import { ShipEquipmentI } from '../EditShipItem/types';
import { EquipmentPropsI } from './types';
import { useEquipment } from './useHook';

const Equipment = ({
  profileId,
  shipId,
  isAddVisible,
  handleAdd,
  equipments,
  fromProfileExperience,
  refetch,
}: EquipmentPropsI) => {
  const {
    handleAddEditEquipment,
    handleDelete,
    shipEquipments,
    isVisible,
    isDeleting,
    setIsVisible,
    loading,
    setDeleteEquimentId,
  } = useEquipment(equipments, handleAdd, shipId, refetch);

  const renderEquipmentItem = (item: ShipEquipmentI) => {
    return (
      <View key={item.id} className="py-3">
        <Text className="text-base font-inter font-medium">{`${item.category.name} | ${item.manufacturerName}`}</Text>
        <View className="flex-row item-center justify-between">
          <Text>{`${item.model}`}</Text>
          {!fromProfileExperience && (
            <View className="flex-row items-center gap-4">
              <Pressable
                onPress={() => {
                  const payload = handleAdd();
                  handleAddEditEquipment(payload, item.id);
                }}
              >
                <EditPencil width={2} height={2} />
              </Pressable>
              <Pressable
                onPress={() => {
                  setDeleteEquimentId(item.id);
                  setIsVisible(true);
                }}
              >
                <DeleteIcon width={2} height={2} />
              </Pressable>
            </View>
          )}
        </View>
        <CustomModal
          isVisible={isVisible}
          onCancel={() => setIsVisible(false)}
          title="Are you sure you want to delete this equipment?"
          confirmText="Delete"
          confirmButtonVariant="danger"
          onConfirm={() => {
            const payload = handleAdd();
            handleDelete(payload);
          }}
          isConfirming={isDeleting}
        />
      </View>
    );
  };

  if (loading) {
    return <ActivityIndicator size="small" color={'#448600'} className="pt-3" />;
  }

  return (
    <>
      {shipEquipments.map((item) => renderEquipmentItem(item))}
      {isAddVisible && !fromProfileExperience && (
        <Pressable
          className="flex-row items-center gap-5 py-5"
          onPress={() => {
            const payload = handleAdd();
            handleAddEditEquipment(payload);
          }}
        >
          <AddItem />
          <Text className="text-[#448600] text-lg font-medium">New Equipment</Text>
        </Pressable>
      )}
    </>
  );
};

export default Equipment;
