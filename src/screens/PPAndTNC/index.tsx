import { View, Text, Pressable } from 'react-native';
import { Controller } from 'react-hook-form';
import Button from '@/src/components/Button';
import Checkbox from '@/src/components/Checkbox';
import PrivacyModal from '@/src/components/PrivacyModal';
import SafeArea from '@/src/components/SafeArea';
import TermsModal from '@/src/components/TermsModal';
import usePPAndTNC from './useHook';

const PPAndTNC = () => {
  const {
    control,
    errors,
    isSubmitting,
    onSubmit,
    showPrivacyModal,
    setShowPrivacyModal,
    showTermsModal,
    setShowTermsModal,
    handleSubmit,
    isValid,
  } = usePPAndTNC();
  return (
    <SafeArea>
      <View className="py-10 px-4 h-full">
        <Controller
          control={control}
          name="ppAndTNC"
          rules={{
            required: 'You must accept the terms and conditions',
          }}
          render={({ field: { onChange, value } }) => (
            <View>
              <Checkbox label="" onValueChange={onChange} checked={value} className="z-10" />
              <View className="flex-row flex-wrap items-center ml-8 -mt-6">
                <Text className="text-sm text-gray-700">I agree to the Navicater </Text>
                <Pressable onPress={() => setShowPrivacyModal(true)} className="z-10">
                  <Text className="text-sm text-[#448600] font-medium underline">
                    Privacy Policy
                  </Text>
                </Pressable>
                <Text className="text-sm text-gray-700"> and </Text>
                <Pressable onPress={() => setShowTermsModal(true)} className="z-10">
                  <Text className="text-sm text-[#448600] font-medium underline">
                    Terms & Conditions
                  </Text>
                </Pressable>
              </View>
              {errors.ppAndTNC && (
                <Text className="text-red-500 text-sm mt-1 ml-8">{errors.ppAndTNC.message}</Text>
              )}
            </View>
          )}
        />
        <View className="absolute bottom-12 left-8 right-8">
          <Button
            onPress={handleSubmit(onSubmit)}
            disabled={!isValid || isSubmitting}
            label="Continue"
            variant={isValid ? 'primary' : 'tertiary'}
            loading={isSubmitting}
            labelClassName="text-base font-medium"
          />
        </View>
      </View>
      <PrivacyModal isVisible={showPrivacyModal} onClose={() => setShowPrivacyModal(false)} />
      <TermsModal isVisible={showTermsModal} onClose={() => setShowTermsModal(false)} />
    </SafeArea>
  );
};

export default PPAndTNC;
