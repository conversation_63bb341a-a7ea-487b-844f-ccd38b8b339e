import { useEffect, useState } from 'react';
import { View } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import Checkbox from '@/src/components/Checkbox';
import ChipInput from '@/src/components/ChipInput';
import EntitySearch from '@/src/components/EntitySearch';
import { selectQuestionIsGeneralDepartment } from '@/src/redux/selectors/question';
import { selectMultipleSelectionsByKey, selectSelectionByKey } from '@/src/redux/selectors/search';
import { clearSelection, setMultipleSelections } from '@/src/redux/slices/entitysearch/searchSlice';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { setIsGeneral } from '@/src/redux/slices/question/questionSlice';
import { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';

const QnaFields = () => {
  const dispatch = useDispatch<AppDispatch>();
  const departmentSelection = useSelector(selectSelectionByKey('department'));
  const topicsSelection = useSelector(
    selectMultipleSelectionsByKey('topic'),
  ) as unknown as SearchResultI[];
  const [localTopics, setLocalTopics] = useState<SearchResultI[]>([]);
  const isGeneral = useSelector(selectQuestionIsGeneralDepartment);

  const MAX_TOPICS = 3;

  useEffect(() => {
    if (!topicsSelection) return;

    setLocalTopics((prev) => {
      const existingIds = new Set(prev.map((t) => t.id));
      const newTopics = topicsSelection.filter((t) => !existingIds.has(t.id));
      const merged = [...prev, ...newTopics];

      if (merged.length > MAX_TOPICS) {
        showToast({
          type: 'error',
          message: 'Topic Limit Reached',
          description: `You can only select up to ${MAX_TOPICS} topics`,
        });

        const limitedTopics = merged.slice(0, MAX_TOPICS);

        dispatch(setMultipleSelections({ key: 'topic', value: limitedTopics }));
        return limitedTopics;
      }

      return merged;
    });
  }, [topicsSelection]);

  const handleTopicRemove = (id: string | number) => {
    const idString = id.toString();
    const updatedTopics = localTopics.filter((t) => t.id !== idString);
    setLocalTopics(updatedTopics);
    dispatch(setMultipleSelections({ key: 'topic', value: updatedTopics }));
  };

  const handleGeneralToggle = (value: boolean) => {
    dispatch(setIsGeneral(value));
    if (value) {
      dispatch(clearSelection('department'));
    }
  };

  return (
    <>
      <ChipInput
        title="Topics"
        placeholder="Add a topic"
        chips={localTopics}
        onRemove={handleTopicRemove}
      />

      <EntitySearch
        title={'Department Type'}
        placeholder={`Enter department type`}
        selectionKey="department"
        data={departmentSelection ? departmentSelection.name : ''}
        className="my-0"
        editable={!isGeneral}
      />
      <View className="flex-row justify-between items-center">
        <View></View>
        <View>
          <Checkbox
            label="General(All Departments)"
            labelClassName="text-sm"
            onValueChange={(value) => {
              handleGeneralToggle(value);
            }}
            checked={isGeneral}
          />
        </View>
      </View>
    </>
  );
};

export default QnaFields;
