import { ActivityIndicator, View } from 'react-native';
import { type RouteProp, useRoute } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import Button from '@/src/components/Button';
import TextInput from '@/src/components/TextInput';
import TextView from '@/src/components/TextView';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import type { AuthStackParamListI } from '@/src/navigation/types';
import useSetUsername from './useHook';

const SetUsernameForm = () => {
  const route = useRoute<RouteProp<AuthStackParamListI, 'SetUsername'>>();
  const currentUser = useSelector(selectCurrentUser);

  const email = route.params?.email || currentUser?.email || '';

  const { methods, isSubmitting, onSubmit, canSubmit, errorMessage, isCheckingUsername } =
    useSetUsername(email);

  const { control, handleSubmit } = methods;

  return (
    <View className="flex-1 bg-white">
      <View className="px-5">
        <View className="my-8">
          <TextView title="Set username" subtitle="Choose a unique username for your account" />
        </View>
        <View>
          <Controller
            control={control}
            name="userName"
            rules={{
              required: 'Username is required',
              validate: (value) => {
                if (!value) return 'Username is required';
                if (value.length < 4) return 'Username must be at least 4 characters';
                if (value.length > 25) return 'Username cannot exceed 25 characters';
                if (/\s/.test(value)) return 'Username cannot contain spaces';
                if (/^[_]/.test(value)) return 'Cannot start with _';
                if (/^[.]/.test(value)) return 'Cannot start with .';
                if (/[.]$/.test(value)) return 'Cannot end with .';
                if (/[_]$/.test(value)) return 'Cannot end with _';
                if (/[_.]{2}/.test(value)) return 'Cannot contain consecutive periods/underscores';
                if (/^[._]+$/.test(value)) return 'Cannot contain only periods or underscores';
                if (!/^[a-zA-Z0-9._]+$/.test(value)) return 'Invalid username format';
                return true;
              },
            }}
            render={({ field: { onChange, value, onBlur }, fieldState: { error } }) => (
              <View>
                <TextInput
                  label="Username"
                  placeholder="Enter username (min 4 characters)"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={error?.message}
                  editable={!isSubmitting}
                />
                {isCheckingUsername && (
                  <View className="mt-2 flex-row items-center gap-2">
                    <ActivityIndicator size="small" color={'#448600'} />
                    <TextView
                      titleClassName="text-sm text-gray-600"
                      title="Checking availability..."
                    />
                  </View>
                )}
              </View>
            )}
          />
        </View>
        <View className="mt-6">
          <Controller
            control={control}
            name="email"
            render={({ field: { value } }) => (
              <TextInput label="Email ID" value={value || email} editable={false} />
            )}
          />
        </View>
        <View className="mt-8">
          <Button
            onPress={handleSubmit(onSubmit)}
            disabled={!canSubmit || isSubmitting}
            label="Save username"
            variant={canSubmit ? 'primary' : 'tertiary'}
            loading={isSubmitting}
            labelClassName="text-base font-medium"
          />
        </View>
      </View>
    </View>
  );
};

export default SetUsernameForm;
