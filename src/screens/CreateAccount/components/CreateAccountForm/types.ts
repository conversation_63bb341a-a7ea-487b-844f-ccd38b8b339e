/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { UseFormReturn } from 'react-hook-form';

export interface CreateAccountFormDataI {
  email: string;
  password: string;
  confirmPassword: string;
  acceptedTerms: boolean;
}

export interface UseCreateAccountFormI {
  methods: UseFormReturn<CreateAccountFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: CreateAccountFormDataI) => Promise<void>;
  showPrivacyModal: boolean;
  setShowPrivacyModal: React.Dispatch<React.SetStateAction<boolean>>;
  showTermsModal: boolean;
  setShowTermsModal: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface CreateAccountPropsI {
  onSignIn?: () => void;
}
