import { Pressable, Text, View } from 'react-native';
import { Controller } from 'react-hook-form';
import Button from '@/src/components/Button';
import Checkbox from '@/src/components/Checkbox';
import PrivacyModal from '@/src/components/PrivacyModal';
import TermsModal from '@/src/components/TermsModal';
import TextInput from '@/src/components/TextInput';
import TextView from '@/src/components/TextView';
import { emailRegex, strongPasswordRegex } from '@/src/consts/regEx';
import type { CreateAccountPropsI } from './types';
import useCreateAccount from './useHook';

const CreateAccountForm = ({ onSignIn }: CreateAccountPropsI) => {
  const {
    methods,
    isSubmitting,
    onSubmit,
    showPrivacyModal,
    setShowPrivacyModal,
    showTermsModal,
    setShowTermsModal,
  } = useCreateAccount();
  const {
    control,
    handleSubmit,
    watch,
    formState: { isValid, errors },
  } = methods;

  const password = watch('password');

  const isFormValid = isValid;
  return (
    <View className="px-5">
      <View className="mt-8 mb-3">
        <TextView
          title="Create account"
          subtitle="Enter your email ID and password to create an account"
        />
      </View>
      <View className="mt-6">
        <Controller
          control={control}
          name="email"
          rules={{
            required: 'Email is required',
            pattern: {
              value: emailRegex,
              message: 'Invalid email address',
            },
          }}
          render={({ field: { onChange, value } }) => (
            <TextInput
              label="Email ID"
              placeholder="Enter email ID"
              value={value}
              onChangeText={onChange}
              error={errors.email?.message}
              editable
              keyboardType="email-address"
              autoCapitalize="none"
            />
          )}
        />
      </View>
      <View className="mt-6">
        <Controller
          control={control}
          name="password"
          rules={{
            required: 'Password is required',
            pattern: {
              value: strongPasswordRegex,
              message:
                'Password must be (8 - 32) characters long, include an uppercase letter, a lowercase letter, a number, and a special character(@, $, !, %, *, ?, &, ^, #).',
            },
          }}
          render={({ field: { onChange, value } }) => (
            <TextInput
              label="New password"
              placeholder="Enter new password"
              value={value}
              onChangeText={onChange}
              type="password"
              error={errors.password?.message}
              autoCorrect={false}
              autoCapitalize="none"
              spellCheck={false}
              autoComplete="off"
            />
          )}
        />
      </View>
      <View className="mt-6">
        <Controller
          control={control}
          name="confirmPassword"
          rules={{
            required: 'Please confirm your password',
            validate: (value) => value === password || 'Passwords do not match',
          }}
          render={({ field: { onChange, value } }) => (
            <TextInput
              label="Confirm new password"
              placeholder="Confirm new password"
              value={value}
              onChangeText={onChange}
              type="password"
              error={errors.confirmPassword?.message}
              autoCorrect={false}
              autoCapitalize="none"
              spellCheck={false}
              autoComplete="off"
            />
          )}
        />
      </View>
      <View className="mt-6">
        <Controller
          control={control}
          name="acceptedTerms"
          rules={{
            required: 'You must accept the terms and conditions',
          }}
          render={({ field: { onChange, value } }) => (
            <View>
              <Checkbox label="" onValueChange={onChange} checked={value} className="z-10" />
              <View className="flex-row flex-wrap items-center ml-8 -mt-6">
                <Text className="text-sm text-gray-700">I agree to the Navicater </Text>
                <Pressable onPress={() => setShowPrivacyModal(true)} className="z-10">
                  <Text className="text-sm text-[#448600] font-medium underline">
                    Privacy Policy
                  </Text>
                </Pressable>
                <Text className="text-sm text-gray-700"> and </Text>
                <Pressable onPress={() => setShowTermsModal(true)} className="z-10">
                  <Text className="text-sm text-[#448600] font-medium underline">
                    Terms & Conditions
                  </Text>
                </Pressable>
              </View>
              {errors.acceptedTerms && (
                <Text className="text-red-500 text-sm mt-1 ml-8">
                  {errors.acceptedTerms.message}
                </Text>
              )}
            </View>
          )}
        />
      </View>
      <View className="mt-8 space-y-4">
        <Button
          onPress={handleSubmit(onSubmit)}
          disabled={!isFormValid || isSubmitting}
          label="Create account"
          variant={isFormValid ? 'primary' : 'tertiary'}
          loading={isSubmitting}
          labelClassName="text-base font-medium"
        />
      </View>
      <View className="flex-row justify-center items-center mt-8">
        <Text className="text-sm text-[#333333]">Already have an account? </Text>
        <Pressable onPress={onSignIn}>
          <Text className="text-sm text-[#448600] font-medium font-inter-medium">Sign in</Text>
        </Pressable>
      </View>
      <PrivacyModal isVisible={showPrivacyModal} onClose={() => setShowPrivacyModal(false)} />
      <TermsModal isVisible={showTermsModal} onClose={() => setShowTermsModal(false)} />
    </View>
  );
};

export default CreateAccountForm;
