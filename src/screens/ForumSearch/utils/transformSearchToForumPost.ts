/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { GlobalSearchQuestionItemI } from '@/src/networks/forum/types';
import type { ForumPostProps } from '@/src/screens/Forum/components/ForumPost/types';

export const transformSearchQuestionToForumPost = (
  question: GlobalSearchQuestionItemI,
): ForumPostProps => {
  const topics = question.topics?.map((topic) => ({
    id: topic.id,
    label: topic.name,
  })) || [];

  const equipment = [];
  if (question.equipmentCategoryName) {
    equipment.push({
      id: question.equipmentCategoryId || '',
      label: question.equipmentCategoryName,
    });
  }
  if (question.equipmentManufacturerName) {
    equipment.push({
      id: question.equipmentManufacturerId || '',
      label: question.equipmentManufacturerName,
    });
  }
  if (question.equipmentModelName) {
    equipment.push({
      id: question.equipmentModelId || '',
      label: question.equipmentModelName,
    });
  }

  // If topics are empty, show equipment; otherwise show topics
  const displayTopics = topics.length > 0 ? topics : undefined;
  const displayEquipment = topics.length === 0 && equipment.length > 0 ? equipment : undefined;

  const profile = {
    id: 'search-profile',
    avatar: null,
    name: question.profileName,
  };

  return {
    postId: question.id,
    communityId: question.communityId,
    profile,
    type: question.type === 'troubleshooting' ? 'troubleshooting' : 'question',
    topics: displayTopics,
    equipment: displayEquipment,
    title: question.title,
    isSolved: false, 
    description: question.description || undefined,
    previewIcons: undefined, 
    upVotes: question.upvoteCount,
    downVotes: 0, 
    answers: question.answerCount,
    commentCount: 0, 
    community: question.communityName,
    endTime: Date.now() + 24 * 60 * 60 * 1000, 
    answerView: false,
    attachments: undefined, 
    canModify: false, 
    isLive: false, 
    isAnonymous: false, 
  };
};
