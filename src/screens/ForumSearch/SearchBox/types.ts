/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import {
  GlobalSearchQuestionItemI,
  GlobalSearchCommunityItemI
} from '@/src/networks/forum/types';

export type ForumSearchType = {
  searchType: 'general' | 'community';
};

export type ForumSearchCategory = 'posts' | 'communities';

export type ForumSearchResponse = {
  data: GlobalSearchQuestionItemI[] | GlobalSearchCommunityItemI[];
  total: number;
};

export type ForumSearchBoxPropsI = {
  onBack: () => void;
  onError?: (error: Error) => void;
};
