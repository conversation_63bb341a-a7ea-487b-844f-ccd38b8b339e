/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import { GlobalSearchCommunityItemI } from '@/src/networks/forum/types';

type SearchCommunityItemPropsI = {
  community: GlobalSearchCommunityItemI;
};

const SearchCommunityItem: React.FC<SearchCommunityItemPropsI> = ({ community }) => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  const handlePress = () => {
    // navigation.navigate('CommunityDetail', { communityId: community.id });
  };

  const getAccessBadgeColor = (access: string) => {
    switch (access) {
      case 'PUBLIC':
        return 'bg-green-100 text-green-800';
      case 'PRIVATE':
        return 'bg-red-100 text-red-800';
      case 'GLOBAL':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Pressable onPress={handlePress} className="bg-white border-b border-gray-200 px-4 py-4">
      <View className="gap-2">
        <View className="flex-row items-center justify-between">
          <Text className="text-lg font-medium text-black flex-1" numberOfLines={1}>
            {community.name}
          </Text>
          <View className={`rounded-full px-2 py-1 ${getAccessBadgeColor(community.access)}`}>
            <Text className="text-xs font-medium">{community.access}</Text>
          </View>
        </View>
        
        {community.description && (
          <Text className="text-base text-gray-700" numberOfLines={2}>
            {community.description}
          </Text>
        )}

        <View className="flex-row items-center gap-4 mt-2">
          <View className="flex-row items-center gap-1">
            <Text className="text-sm text-gray-600">👥 {community.memberCount} members</Text>
          </View>
          <View className="flex-row items-center gap-1">
            <Text className="text-sm text-gray-600">❓ {community.questionCount} questions</Text>
          </View>
          {community.isRestricted && (
            <View className="bg-orange-100 rounded-full px-2 py-1">
              <Text className="text-xs text-orange-800">Restricted</Text>
            </View>
          )}
        </View>
      </View>
    </Pressable>
  );
};

export default SearchCommunityItem;
