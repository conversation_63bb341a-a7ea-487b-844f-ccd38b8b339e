/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch } from 'react-redux';
import type { AppDispatch } from '@/src/redux/store';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import { GlobalSearchQuestionItemI } from '@/src/networks/forum/types';
import { fetchForumQuestionDetail } from '@/src/redux/slices/forum/forumSlice';
import { renderTextWithHighlight } from '@/src/utilities/textHighlight';
import UserAvatar from '@/src/components/UserAvatar';
import UpVote from '@/src/assets/svgs/UpVote';
import Bulb from '@/src/assets/svgs/Bulb';
import Comment from '@/src/assets/svgs/Comment';

type SearchForumPostPropsI = {
  question: GlobalSearchQuestionItemI;
  searchText?: string;
};

const SearchForumPost: React.FC<SearchForumPostPropsI> = ({ question, searchText }) => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch<AppDispatch>();

  const handlePress = async () => {
    await dispatch(fetchForumQuestionDetail({ questionId: question.id }));
    navigation.navigate('ForumAnswers', { postId: question.id });
  };

  return (
    <Pressable onPress={handlePress} className="bg-white border-b border-gray-200 px-4 py-4">
      <View className="gap-3">
        {/* Header with profile info */}
        <View className="flex-row items-center gap-3">
          <View className="h-10 w-10 bg-gray-100 rounded-full justify-center items-center">
            <UserAvatar
              avatarUri={null}
              height={40}
              width={40}
              name={question.profileName}
            />
          </View>
          <View className="flex-1">
            <View className="flex-row items-center gap-2">
              <Text className="text-sm font-medium text-gray-900">{question.profileName}</Text>
              <Text className="text-sm text-gray-400">•</Text>
              <Text className="text-sm text-gray-600">{question.communityName}</Text>
            </View>
            <Text className="text-xs text-gray-500">
              {question.type === 'troubleshooting' ? 'Troubleshooting' : 'Question'}
            </Text>
          </View>
        </View>

        {/* Title with highlighting */}
        <Text className="text-lg font-semibold text-black" numberOfLines={2}>
          {renderTextWithHighlight(question.title, searchText)}
        </Text>
        
        {/* Description with highlighting */}
        {question.description && (
          <Text className="text-base text-gray-700" numberOfLines={3}>
            {renderTextWithHighlight(question.description, searchText)}
          </Text>
        )}

        {/* Topics */}
        {question.topics && question.topics.length > 0 && (
          <View className="flex-row flex-wrap gap-1">
            {question.topics.slice(0, 3).map((topic) => (
              <View key={topic.id} className="bg-gray-100 rounded-lg px-2 py-1">
                <Text className="text-xs text-gray-700">{topic.name}</Text>
              </View>
            ))}
            {question.topics.length > 3 && (
              <View className="bg-gray-100 rounded-lg px-2 py-1">
                <Text className="text-xs text-gray-700">+{question.topics.length - 3} more</Text>
              </View>
            )}
          </View>
        )}

        {/* Equipment info */}
        {(question.equipmentCategoryName || question.equipmentModelName || question.equipmentManufacturerName) && (
          <View className="flex-row flex-wrap gap-1">
            {question.equipmentCategoryName && (
              <View className="bg-blue-50 rounded-lg px-2 py-1">
                <Text className="text-xs text-blue-700">{question.equipmentCategoryName}</Text>
              </View>
            )}
            {question.equipmentManufacturerName && (
              <View className="bg-blue-50 rounded-lg px-2 py-1">
                <Text className="text-xs text-blue-700">{question.equipmentManufacturerName}</Text>
              </View>
            )}
            {question.equipmentModelName && (
              <View className="bg-blue-50 rounded-lg px-2 py-1">
                <Text className="text-xs text-blue-700">{question.equipmentModelName}</Text>
              </View>
            )}
          </View>
        )}

        {/* Stats */}
        <View className="flex-row items-center gap-4">
          <View className="flex-row items-center gap-1">
            <UpVote width={16} height={16} stroke="#6B7280" />
            <Text className="text-sm text-gray-600">{question.upvoteCount}</Text>
          </View>
          <View className="flex-row items-center gap-1">
            <Bulb width={16} height={16} stroke="#6B7280" />
            <Text className="text-sm text-gray-600">{question.answerCount}</Text>
          </View>
          <View className="flex-row items-center gap-1">
            <Comment width={16} height={16} stroke="#6B7280" />
            <Text className="text-sm text-gray-600">Comments</Text>
          </View>
        </View>
      </View>
    </Pressable>
  );
};

export default SearchForumPost;
