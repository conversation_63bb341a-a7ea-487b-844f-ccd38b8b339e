/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import { GlobalSearchQuestionItemI } from '@/src/networks/forum/types';
import { fetchForumQuestionDetail } from '@/src/redux/slices/forum/forumSlice';
import { useDispatch } from 'react-redux';

type SearchQuestionItemPropsI = {
  question: GlobalSearchQuestionItemI;
};

const SearchQuestionItem: React.FC<SearchQuestionItemPropsI> = ({ question }) => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch();

  const handlePress = async () => {
    await dispatch(fetchForumQuestionDetail({ questionId: question.id }));
    navigation.navigate('ForumAnswers', { postId: question.id });
  };

  return (
    <Pressable onPress={handlePress} className="bg-white border-b border-gray-200 px-4 py-4">
      <View className="gap-2">
        <View className="flex-row items-center gap-2">
          <Text className="text-sm text-gray-600">{question.communityName}</Text>
          <Text className="text-sm text-gray-400">•</Text>
          <Text className="text-sm text-gray-600">{question.profileName}</Text>
        </View>
        
        <Text className="text-lg font-medium text-black" numberOfLines={2}>
          {question.title}
        </Text>
        
        {question.description && (
          <Text className="text-base text-gray-700" numberOfLines={2}>
            {question.description}
          </Text>
        )}

        {question.topics && question.topics.length > 0 && (
          <View className="flex-row flex-wrap gap-1 mt-1">
            {question.topics.slice(0, 3).map((topic) => (
              <View key={topic.id} className="bg-gray-100 rounded-lg px-2 py-1">
                <Text className="text-xs text-gray-700">{topic.name}</Text>
              </View>
            ))}
          </View>
        )}

        <View className="flex-row items-center gap-4 mt-2">
          <View className="flex-row items-center gap-1">
            <Text className="text-sm text-gray-600">↑ {question.upvoteCount}</Text>
          </View>
          <View className="flex-row items-center gap-1">
            <Text className="text-sm text-gray-600">💡 {question.answerCount}</Text>
          </View>
        </View>
      </View>
    </Pressable>
  );
};

export default SearchQuestionItem;
