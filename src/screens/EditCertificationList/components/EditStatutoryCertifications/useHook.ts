import { useCallback, useState } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { handleError } from '@/src/utilities/errors/errors';
import { navigate } from '@/src/utilities/navigation';
import { showToast } from '@/src/utilities/toast';
import type { ProfileStackParamsListI } from '@/src/navigation/types';
import {
  deleteCertificationAPI,
  fetchStatutoryCertificationsAPI,
} from '@/src/networks/career/certification';
import type { CertificationI } from '../EditCertificationList/types';

export const useEditStatutory = (profileId: string) => {
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const pageSize = 10;
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();
  const [isVisible, setIsVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteCertificationId, setDeleteCertificationId] = useState<string>('');
  const [error, setError] = useState<Error | null>(null);
  const [certifications, setCertifications] = useState<CertificationI[]>([]);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) throw error;

  const fetchCertifications = async (pageNumber: number, isLoadMore = false) => {
    try {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const result = await fetchStatutoryCertificationsAPI(profileId, pageNumber, pageSize);

      if (isLoadMore) {
        setCertifications((prev) => [...prev, ...result]);
      } else {
        setCertifications(result);
      }

      setHasMore(result.length >= pageSize);
      setPage(pageNumber);
    } catch (error) {
      triggerErrorBoundary(
        new Error(
          'Failed to load statutory certifications: ' +
            (error instanceof Error ? error.message : 'Unknown error'),
        ),
      );
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchCertifications(0);
    }, [profileId]),
  );

  const handleLoadMore = () => {
    if (!loadingMore && !loading && hasMore) {
      fetchCertifications(page + 1, true);
    }
  };

  const onEditCertification = (certificationId: string) => {
    navigate('EditCertificationItem', {
      profileId,
      certificationId,
    });
  };

  const onDeleteCertification = async () => {
    setIsDeleting(true);
    try {
      await deleteCertificationAPI(deleteCertificationId);
      setCertifications((prev) => prev.filter((item) => item.id !== deleteCertificationId));
      showToast({
        message: 'Success',
        description: 'Cerification deleted successfully',
        type: 'success',
      });
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Error',
            description: 'Failed to delete certification',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return {
    onEditCertification,
    onDeleteCertification,
    isVisible,
    setIsVisible,
    setDeleteCertificationId,
    isDeleting,
    loading,
    loadingMore,
    hasMore,
    handleLoadMore,
    certifications,
  };
};
