import type React from 'react';
import type { Dispatch, SetStateAction } from 'react';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import type { ExperienceFormDataI } from './schema';

export interface EditExperienceItemPropsI {
  onBack: () => void;
  profileId: string;
  experienceId?: string;
}

export interface DesignationsI {
  indexKey: string;
  id: string;
  name: string;
  dataType: 'raw' | 'master';
}

export interface ApiResponseTypeI {
  entity: SearchResultI;
  designations: any[];
}

export interface FetchedExperienceI {
  company: SearchResultI;
  designations: any[];
}

export interface FieldTypeI {
  designation: any;
  fromDate: string;
  id: string;
  ships: any[];
  toDate?: string;
}

export interface FieldShipType {
  fromDate: string;
  id: string;
  name: string;
  ship: any;
  subVesselType: any;
  toDate?: string;
  isPresent?: boolean;
}

export interface DesignationWithDateI {
  designation?: any;
  fromDate?: string;
  toDate?: string;
  id?: string;
  ships?: any[];
  isPresent?: boolean;
  name?: string;
  experienceDesignationId?: string;
}

export interface ShipItemProps {
  field: FieldTypeI;
  onAddEdit: (field: FieldTypeI, shipId?: string) => void;
  deleteShip: (field: FieldTypeI, shipId: string) => void;
  isDeleting: boolean;
  company: SearchResultI;
  index: number;
}

export interface UseEditExperienceItemReturn {
  designations: DesignationsI[];
  isSubmitting: boolean;
  loading: boolean;
  isAddingDesignation: boolean;
  handleSubmit: () => Promise<void>;
  handleAddDesignation: () => void;
  handleAddEditShip: (field: FieldTypeI, shipId?: string) => void;
  handleDeleteShip: (field: FieldTypeI, shipId: string) => Promise<void>;
  isDeleting: boolean;
  localDesignations: DesignationWithDateI[];
  getDateChangeHandler: (
    index: number,
    field: 'fromDate' | 'toDate',
  ) => React.Dispatch<React.SetStateAction<Date>>;
  localEntity: SearchResultI | undefined;
  setLocalDesignations: Dispatch<SetStateAction<DesignationWithDateI[]>>;
  getFieldError: (fieldName: string, index: number) => string | undefined;
  handlePresentCheckbox: (index: number, isPresent: boolean) => void;
  designationSelections: (SearchResultI | undefined)[];
}

export type { ExperienceFormDataI };
