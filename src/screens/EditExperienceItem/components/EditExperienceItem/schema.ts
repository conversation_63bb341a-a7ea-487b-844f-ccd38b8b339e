import { z } from 'zod';

export const EntitySchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  dataType: z.string().optional(),
});

const getTodayString = (): string => {
  const today = new Date();
  return today.toISOString().split('T')[0];
};

const DesignationSchema = z
  .object({
    id: z.string().optional(),
    designation: z.any().optional(),
    name: z.string().optional(),
    experienceDesignationId: z.string().optional(),
    fromDate: z.string().min(1, 'From Date is required'),
    toDate: z.string().optional(),
    isPresent: z.boolean().optional(),
    ships: z.array(z.any()),
  })
  .refine(
    (data) => {
      if (data.isPresent) return true;
      if (!data.fromDate || !data.toDate) return true;
      return new Date(data.fromDate) <= new Date(data.toDate);
    },
    {
      message: 'To date must be later than or equal to From date',
      path: ['toDate'],
    },
  )
  .refine(
    (data) => {
      if (data.isPresent) return true;
      if (!data.toDate) return true;
      const today = new Date(getTodayString());
      return new Date(data.toDate) <= today;
    },
    {
      message: 'To date cannot be in the future',
      path: ['toDate'],
    },
  );

export const editExperienceSchema = z.object({
  company: EntitySchema.optional(),
  designations: z.array(DesignationSchema),
});

export type ExperienceFormDataI = z.infer<typeof editExperienceSchema>;
