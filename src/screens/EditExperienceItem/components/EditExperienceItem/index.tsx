import type React from 'react';
import { useState, useMemo } from 'react';
import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Checkbox from '@/src/components/Checkbox';
import DatePicker from '@/src/components/DatePicker';
import EntitySearch from '@/src/components/EntitySearch';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import TextView from '@/src/components/TextView';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import AddItem from '@/src/assets/svgs/AddItem';
import EditPencil from '@/src/assets/svgs/EditPencil';
import DeleteIcon from '@/src/assets/svgs/TrashBin';
import { toMonthYear } from '../utils';
import type {
  EditExperienceItemPropsI,
  FieldTypeI,
  ShipItemProps,
  UseEditExperienceItemReturn,
} from './types';
import useEditExperienceItem from './useHook';

const ShipItem: React.FC<ShipItemProps> = ({
  field,
  onAddEdit,
  deleteShip,
  isDeleting,
  company,
  index,
}) => {
  const [shipDeleteId, setShipDeleteId] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const designationSelection = useSelector(selectSelectionByKey(`designation-${index}`));
  const canAddShips =
    company?.id && (designationSelection?.id || field?.designation?.id) && field?.fromDate;

  return (
    <>
      <View className="px-4 py-2 bg-[#F5F5F5] rounded-lg">
        <View className="flex-row items-center justify-between">
          <TextView subtitle="Ship details" subtitleClassName="font-bold text-black" />
          {canAddShips && (
            <Pressable onPress={() => onAddEdit(field)}>
              <AddItem />
            </Pressable>
          )}
        </View>
        {(field.ships ?? []).length === 0 && (
          <NotFound
            imageStyle={{ width: '50%', height: 100 }}
            fullScreen={false}
            className="py-5"
          />
        )}
        {(field.ships ?? []).map((ship: any) => {
          return (
            <View className="py-3" key={ship.id}>
              <TextView subtitle={`${ship.name}`} subtitleClassName="font-bold" />
              <View className="flex-row items-center justify-between">
                <TextView
                  subtitle={`${toMonthYear(ship.fromDate)} - ${
                    ship.isPresent || !ship.toDate ? 'Present' : toMonthYear(ship.toDate)
                  }`}
                />
                <View className="flex-row items-center gap-4">
                  <Pressable onPress={() => onAddEdit(field, ship.id)}>
                    <EditPencil width={2.3} height={2.3} />
                  </Pressable>
                  <Pressable
                    onPress={() => {
                      setShipDeleteId(ship.id);
                      setIsVisible(true);
                    }}
                  >
                    <DeleteIcon width={2.3} height={2.3} />
                  </Pressable>
                </View>
              </View>
            </View>
          );
        })}
      </View>
      <CustomModal
        isVisible={isVisible}
        onCancel={() => {
          setShipDeleteId(null);
          setIsVisible(false);
        }}
        title="Are you sure you want to delete this ship?"
        confirmText="Delete"
        confirmButtonVariant="danger"
        onConfirm={() => {
          if (shipDeleteId) {
            deleteShip(field, shipDeleteId);
            setIsVisible(false);
          }
        }}
        isConfirming={isDeleting}
      />
    </>
  );
};

const EditExperienceItem: React.FC<EditExperienceItemPropsI> = ({
  onBack,
  profileId,
  experienceId,
}) => {
  const [removingIndex, setRemovingIndex] = useState<number | null>(null);
  const {
    isSubmitting,
    loading,
    designations,
    isAddingDesignation,
    handleAddDesignation,
    handleAddEditShip,
    handleDeleteShip,
    isDeleting,
    localDesignations,
    setLocalDesignations,
    getDateChangeHandler,
    localEntity,
    handleSubmit,
    getFieldError,
    handlePresentCheckbox,
  }: UseEditExperienceItemReturn = useEditExperienceItem(profileId, experienceId);

  const designationKeys = useMemo(() => {
    return localDesignations.map((_, index) => `designation-${index}`);
  }, [localDesignations.length]);

  const designation0 = useSelector(selectSelectionByKey(designationKeys[0] || ''));
  const designation1 = useSelector(selectSelectionByKey(designationKeys[1] || ''));
  const designation2 = useSelector(selectSelectionByKey(designationKeys[2] || ''));
  const designation3 = useSelector(selectSelectionByKey(designationKeys[3] || ''));
  const designation4 = useSelector(selectSelectionByKey(designationKeys[4] || ''));
  const designation5 = useSelector(selectSelectionByKey(designationKeys[5] || ''));
  const designation6 = useSelector(selectSelectionByKey(designationKeys[6] || ''));
  const designation7 = useSelector(selectSelectionByKey(designationKeys[7] || ''));
  const designation8 = useSelector(selectSelectionByKey(designationKeys[8] || ''));
  const designation9 = useSelector(selectSelectionByKey(designationKeys[9] || ''));

  const designationSelections = [
    designation0,
    designation1,
    designation2,
    designation3,
    designation4,
    designation5,
    designation6,
    designation7,
    designation8,
    designation9,
  ];

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-white" showsVerticalScrollIndicator={false}>
      <View className="px-4">
        <View className="flex-row items-center justify-between py-4">
          <BackButton onBack={onBack} label="Edit Experience" />
          <Pressable onPress={handleSubmit} disabled={isSubmitting}>
            <Text className="text-lg font-medium text-[#448600]">
              {isSubmitting ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        </View>
        <EntitySearch
          title="Company"
          placeholder="Enter Company ..."
          selectionKey="entity/organisation"
          data={localEntity?.name}
        />
        <Pressable onPress={handleAddDesignation} disabled={isAddingDesignation}>
          <View className="flex-row justify-between mt-4 items-center">
            <TextView subtitle="Designation Details" subtitleClassName="font-bold text-black" />
            <View className="flex-row items-center gap-2">
              {isAddingDesignation && <ActivityIndicator size="small" color="#448600" />}
              <AddItem />
            </View>
          </View>
        </Pressable>
        {localDesignations.map((item: any, index: number) => {
          const fromDateError = getFieldError('fromDate', index);
          const toDateError = getFieldError('toDate', index);
          const isRemoving = removingIndex === index;
          const designationSelection = designationSelections[index];

          return (
            <View className="" key={`designation-${index}-${item.id || 'new'}`}>
              <EntitySearch
                title={`Designation ${index + 1}`}
                placeholder="Enter Designation"
                selectionKey={`designation-${index}`}
                data={designationSelection?.name || item?.designation?.name || ''}
              />
              <View className="flex-row mb-6 mt-4">
                <View className="flex-1 mr-2">
                  <DatePicker
                    title="From"
                    selectedDate={item.fromDate || ''}
                    onDateChange={getDateChangeHandler(index, 'fromDate')}
                    showMonthYear={true}
                  />
                  {fromDateError && (
                    <Text className="text-red-500 text-sm mt-1">{fromDateError}</Text>
                  )}
                </View>
                <View className="flex-1 mr-2">
                  <DatePicker
                    title="To"
                    selectedDate={item.isPresent ? '' : item.toDate || ''}
                    onDateChange={getDateChangeHandler(index, 'toDate')}
                    showMonthYear={true}
                    disabled={item.isPresent}
                  />
                  {toDateError && <Text className="text-red-500 text-sm mt-1">{toDateError}</Text>}
                  <Checkbox
                    label="Present"
                    className="pt-3"
                    labelClassName="text-base text-sm"
                    onValueChange={(checked) => handlePresentCheckbox(index, checked)}
                    checked={item.isPresent || false}
                  />
                </View>
              </View>
              <ShipItem
                field={item as FieldTypeI}
                onAddEdit={(field: FieldTypeI, shipId?: string) => handleAddEditShip(field, shipId)}
                deleteShip={(field: FieldTypeI, shipId: string) => handleDeleteShip(field, shipId)}
                isDeleting={isDeleting}
                company={localEntity as SearchResultI}
                index={index}
              />
              <Pressable
                onPress={async () => {
                  setRemovingIndex(index);
                  setLocalDesignations((prev: any[]) =>
                    prev.filter((_: any, i: number) => i !== index),
                  );
                  setRemovingIndex(null);
                }}
                disabled={isRemoving}
                className={`flex-row items-center self-end mb-4 gap-2 mt-2 ${isRemoving ? 'opacity-50' : ''}`}
              >
                <DeleteIcon color="red" width={1.75} height={1.75} />
                <Text className="text-red-500">{isRemoving ? 'Removing...' : 'Remove'}</Text>
              </Pressable>
            </View>
          );
        })}
      </View>
    </ScrollView>
  );
};

export default EditExperienceItem;
