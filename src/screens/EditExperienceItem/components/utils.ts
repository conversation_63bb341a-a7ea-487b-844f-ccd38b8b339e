import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import {
  ExperienceDesignationI,
  ExperienceFetchForClientResultI,
  ExperienceItemI,
  ExperienceModuleCreateOneParamsI,
  ExperienceModuleUpdateOneParamsI,
  OprTypeE,
} from '@/src/redux/slices/experience/types';
import { DesignationWithDateI, FieldTypeI } from './EditExperienceItem/types';

export const toMonthYear = (dateStr: string | null): string => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  const options: Intl.DateTimeFormatOptions = {
    month: 'short',
    year: 'numeric',
  };
  return date.toLocaleDateString('en-US', options);
};

export const generateAddExperiencePayload = (
  entity: SearchResultI,
  designations: (SearchResultI & { fromDate: string; toDate: string | null })[],
): ExperienceModuleCreateOneParamsI[] => {
  const payload: ExperienceModuleCreateOneParamsI[] = [
    {
      opr: 'CREATE',
      entity: entity,
      designations:
        designations?.map((item: any) => {
          return {
            opr: 'CREATE',
            fromDate: item.fromDate,
            toDate: item.toDate,
            designation: {
              ...item,
              opr: 'CREATE',
            },
          };
        }) || [],
    },
  ];
  return payload;
};

export const generateEditExperiencePayload = (
  experience: ExperienceItemI,
  selectedExperience: ExperienceFetchForClientResultI | undefined,
): ExperienceModuleUpdateOneParamsI => {
  const existingDesignations: any[] = selectedExperience?.designations || [];
  const currentDesignations: ExperienceDesignationI[] = experience.designations || [];
  const payload: ExperienceModuleUpdateOneParamsI = [
    {
      opr: 'UPDATE',
      id: experience.id,
      entity: {
        id: experience.entity.id,
        dataType: experience.entity.dataType,
      },
      designations: [
        ...currentDesignations.flatMap((item: ExperienceDesignationI) => {
          const existingItem: any = existingDesignations.find(
            (d: any) => d.designation.id === item.designation.id,
          );

          const isCreateDuplicate: boolean = existingItem && !item.id;

          if (!existingItem || isCreateDuplicate) {
            return [
              {
                opr: 'CREATE' as OprTypeE | undefined,
                id: item?.experienceDesignationId,
                fromDate: item.fromDate,
                toDate: item.toDate,
                designation: {
                  id: item.designation.id,
                  dataType: item.designation.dataType,
                },
              },
            ];
          }

          const hasChanged: boolean =
            item.fromDate !== existingItem.fromDate || item.toDate !== existingItem.toDate;

          if (hasChanged) {
            return [
              {
                opr: 'UPDATE' as OprTypeE | undefined,
                id: item?.id,
                fromDate: item.fromDate,
                toDate: item.toDate,
                designation: {
                  id: item.designation.id,
                  dataType: item.designation.dataType,
                },
              },
            ];
          }

          return [];
        }),

        ...existingDesignations
          .filter(
            (existing: any) =>
              !currentDesignations.some(
                (d: ExperienceDesignationI) => d.designation.id === existing.designation.id,
              ),
          )
          .map((deleted: any) => ({
            opr: 'DELETE' as const,
            id: deleted.id,
          })),
      ],
    },
  ];
  return payload;
};

export const generateExperiencePayload = (
  localEntity: SearchResultI,
  initialEntity: SearchResultI,
  localDesignations: DesignationWithDateI[],
  initialDesignations: DesignationWithDateI[],
  experienceId?: string,
  field?: FieldTypeI,
): any[] => {
  const experience: any = {
    opr: experienceId ? 'UPDATE' : 'CREATE',
  };

  if (experienceId) {
    experience.id = experienceId;
  }

  if (localEntity.id !== initialEntity?.id) {
    experience.entity = localEntity;
  } else {
    experience.opr = 'NESTED_OPR';
  }

  const result: any[] = [];
  let fieldDesignationHandled: boolean = false;

  if (initialDesignations.length > 0) {
    initialDesignations.forEach((iDesignation: DesignationWithDateI) => {
      if (iDesignation.id !== undefined) {
        const id: string = iDesignation.id;
        const matchingLocal: DesignationWithDateI | undefined = localDesignations.find(
          (lDesignation: DesignationWithDateI) => lDesignation.id === id,
        );

        if (!matchingLocal) {
          result.push({
            id: id,
            opr: 'DELETE',
          });
        } else {
          const fieldsChanged: boolean =
            JSON.stringify(iDesignation.designation) !==
              JSON.stringify(matchingLocal.designation) ||
            iDesignation.fromDate !== matchingLocal.fromDate ||
            iDesignation.toDate !== matchingLocal.toDate;

          if (fieldsChanged) {
            if (field?.id && id === field.id) {
              fieldDesignationHandled = true;
            }
            result.push({
              id: id,
              opr: 'UPDATE',
              designation: matchingLocal.designation,
              fromDate: matchingLocal.fromDate,
              toDate: matchingLocal.toDate ?? null,
            });
          }
        }
      }
    });
  }

  localDesignations.forEach((lDesignation: DesignationWithDateI) => {
    if (!lDesignation.id) {
      result.push({
        opr: 'CREATE',
        designation: lDesignation.designation,
        fromDate: lDesignation.fromDate,
        toDate: lDesignation.toDate ?? null,
      });
    }
  });

  if (field?.id && !fieldDesignationHandled) {
    const fieldDesignation: DesignationWithDateI | undefined = localDesignations.find(
      (d: DesignationWithDateI) => d.id === field.id,
    );
    if (fieldDesignation) {
      result.push({
        id: field.id,
        opr: 'NESTED_OPR',
      });
    }
  }

  experience.designations = result;

  return [experience];
};
