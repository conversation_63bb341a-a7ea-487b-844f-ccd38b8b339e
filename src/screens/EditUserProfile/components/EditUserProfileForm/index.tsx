import { useCallback, useState } from 'react';
import { Pressable, Text, View, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import EntitySearch from '@/src/components/EntitySearch';
import TextInput from '@/src/components/TextInput';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import AvatarPicker from '../AvatarPicker';
import type { EditUserProfileFormPropsI } from './types';
import useEditUserProfile from './useHook';

const EditUserProfileForm = ({ onBack }: EditUserProfileFormPropsI) => {
  const {
    methods,
    isSubmitting,
    onSubmit,
    handleAvatarChange,
    handleAvatarDelete,
    avatarFile,
    isAvatarDeleted,
    loading,
  } = useEditUserProfile();

  const [charCount, setCharCount] = useState(0);
  const currentUser = useSelector(selectCurrentUser);
  const entitySelection = useSelector(selectSelectionByKey('entity'));
  const designationSelection = useSelector(selectSelectionByKey('designation'));

  const {
    control,
    handleSubmit,
    setValue,
    trigger,
    formState: { errors },
  } = methods;

  useFocusEffect(
    useCallback(() => {
      if (entitySelection) {
        setValue('entity', entitySelection);
        trigger('entity');
      }
      if (designationSelection) {
        setValue('designation', designationSelection);
        trigger('designation');
      }
      if (currentUser.description) {
        setCharCount(currentUser.description.length);
      }
    }, [entitySelection, designationSelection, currentUser.description]),
  );

  const getAvatarValue = () => {
    if (isAvatarDeleted) return null;
    if (avatarFile?.path) return avatarFile.path;
    return currentUser.avatar;
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <View className="px-4">
        <View className="flex-row items-center justify-between pb-4">
          <BackButton onBack={onBack} label="" />
          <Pressable onPress={handleSubmit(onSubmit)} disabled={isSubmitting}>
            <Text className="font-inter text-lg font-medium text-[#448600]">
              {isSubmitting ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        </View>
      </View>

      <ScrollView
        className="flex-1 px-4"
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={{ paddingBottom: 50 }}
      >
        <Controller
          control={control}
          name="avatar"
          render={() => (
            <AvatarPicker
              value={getAvatarValue()}
              onChange={handleAvatarChange}
              onDelete={handleAvatarDelete}
              size={120}
              loading={loading}
            />
          )}
        />

        <Controller
          control={control}
          name="name"
          rules={{
            required: 'Name is required',
          }}
          render={({ field: { onChange, value } }) => (
            <TextInput
              label="Name"
              placeholder="Enter your name"
              value={value}
              onChangeText={onChange}
              error={errors.name?.message}
              className="mb-4"
            />
          )}
        />

        <View className="mb-4">
          <Controller
            control={control}
            name="designation"
            rules={{
              required: 'Rank is required',
            }}
            render={({ fieldState: { error } }) => (
              <EntitySearch
                placeholder="Search rank"
                selectionKey="designation"
                title="Rank"
                data={designationSelection?.name || currentUser.designation?.name}
                error={error?.message}
              />
            )}
          />
        </View>

        <View className="mb-4">
          <Controller
            control={control}
            name="entity"
            render={({ fieldState: { error } }) => (
              <EntitySearch
                placeholder="Search company"
                selectionKey="entity"
                title="Company"
                data={entitySelection?.name || currentUser.organisation?.name}
                error={error?.message}
              />
            )}
          />
        </View>

        <View className="relative mb-4">
          <Controller
            control={control}
            name="description"
            render={({ field: { onChange, value } }) => (
              <>
                <TextInput
                  label="Description"
                  placeholder="Write a short bio about yourself..."
                  value={value}
                  onChangeText={(text) => {
                    if (text.length <= 255) {
                      onChange(text);
                      setCharCount(text.length);
                    }
                  }}
                  type="textarea"
                  numberOfLines={6}
                  error={errors.description?.message}
                  maxLength={255}
                  multiline={true}
                  textAlignVertical="top"
                />
                <View className="absolute bottom-2 right-2 px-2 py-1 rounded-full bg-gray-100">
                  <Text
                    className={`text-xs font-medium ${
                      charCount > 230
                        ? charCount > 245
                          ? 'text-red-600'
                          : 'text-orange-600'
                        : 'text-gray-600'
                    }`}
                  >
                    {charCount}/255
                  </Text>
                </View>
              </>
            )}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default EditUserProfileForm;
