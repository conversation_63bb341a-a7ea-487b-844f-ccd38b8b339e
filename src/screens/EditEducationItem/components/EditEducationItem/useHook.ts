import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectMultipleSelectionsByKey } from '@/src/redux/selectors/search';
import { addEducation, editEducation } from '@/src/redux/slices/about/aboutSlice';
import {
  clearMultipleSelections,
  clearSelection,
} from '@/src/redux/slices/entitysearch/searchSlice';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { IdTypeI } from '@/src/types/common/data';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import {
  addEducationAPI,
  editEducationAPI,
  fetchEducationAPI,
} from '@/src/networks/career/education';
import { EducationFormDataI, UseEditEducationItemI } from './types';

export const useEditEducationItem = (
  profileId?: string,
  educationId?: string,
): UseEditEducationItemI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();
  const [localSkills, setLocalSkills] = useState<SearchResultI[]>([]);
  const skillsSelection = useSelector(selectMultipleSelectionsByKey('skill'));
  const [initialSkills, setInitialSkills] = useState<SearchResultI[]>([]);
  const [loading, setLoading] = useState(false);
  const [isPresent, setIsPresent] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const dispatch = useDispatch<AppDispatch>();

  const methods = useForm<EducationFormDataI>({
    mode: 'onChange',
    defaultValues: {
      institution: {},
      degree: {},
      fromDate: '',
      toDate: '',
      skills: [],
    },
  });

  useEffect(() => {
    if (educationId) {
      const fetchEducation = async () => {
        try {
          setLoading(true);
          const response = await fetchEducationAPI(educationId);

          const fetchedEducation: EducationFormDataI = {
            institution: response.entity,
            degree: response.degree,
            fromDate: response.fromDate,
            toDate: response.toDate,
            skills: response.skills,
          };

          if (!response.toDate) {
            setIsPresent(true);
          }

          setLocalSkills((response.skills as unknown as SearchResultI[]) || []);
          setInitialSkills(response.skills);
          methods.reset(fetchedEducation);
        } catch (err) {
          triggerErrorBoundary(
            new Error(
              'Failed to load education details: ' +
                (err instanceof Error ? err.message : 'Unknown error'),
            ),
          );
        } finally {
          setLoading(false);
        }
      };

      fetchEducation();
    }
  }, [educationId, methods]);

  useEffect(() => {
    if (!skillsSelection) return;

    setLocalSkills((prev) => {
      const existingIds = new Set(prev.map((s) => s.id));
      const merged = [...prev, ...skillsSelection.filter((s) => !existingIds.has(s.id))];
      return merged;
    });
  }, [skillsSelection]);

  const onSubmit = async (data: EducationFormDataI) => {
    try {
      setIsSubmitting(true);
      const payload = transformDataForCreate(data, localSkills);
      if (educationId) {
        const deletedSkills = initialSkills
          .filter((initial) => !localSkills.some((local) => local.id === initial.id))
          .map(({ id, dataType }) => ({ id, dataType }));

        const addedSkills = localSkills
          .filter((local) => !initialSkills.some((initial) => initial.id === local.id))
          .map(({ id, dataType }) => ({ id, dataType }));

        const payload = transformDataForEdit(data, addedSkills, deletedSkills);
        await editEducationAPI(educationId, payload);
      } else {
        await addEducationAPI(payload);
        setLocalSkills([]);
      }

      showToast({
        message: 'Success',
        description: `Added new Education successfully`,
        type: 'success',
      });
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Save Education',
            description: 'Unable to save Education',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePresentCheckbox = () => {
    setIsPresent(!isPresent);
    methods.setValue('toDate', null);
  };

  const clearFields = () => {
    dispatch(clearSelection('entity'));
    dispatch(clearSelection('degree'));
    dispatch(clearMultipleSelections('skill'));
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    navigation,
    localSkills,
    setLocalSkills,
    loading,
    isPresent,
    handlePresentCheckbox,
    clearFields,
    isSubmitted,
    setIsSubmitted,
  };
};

const transformDataForCreate = (
  data: EducationFormDataI,
  skills: { id: string; dataType: 'raw' | 'master' }[],
) => {
  return {
    institute: {
      id: data.institution.id,
      dataType: data.institution.dataType,
    },
    degree: {
      id: data.degree.id,
      dataType: data.degree.dataType,
    },
    fromDate: data.fromDate,
    toDate: data.toDate,
    skills: skills,
  };
};

const transformDataForEdit = (
  data: EducationFormDataI,
  addedSkills: IdTypeI[],
  deletedSkills: IdTypeI[],
) => {
  return {
    institute: {
      id: data.institution.id,
      dataType: data.institution.dataType,
    },
    degree: {
      id: data.degree.id,
      dataType: data.degree.dataType,
    },
    fromDate: data.fromDate,
    toDate: data.toDate,
    skillsToAdd: addedSkills,
    skillsToDelete: deletedSkills,
  };
};
