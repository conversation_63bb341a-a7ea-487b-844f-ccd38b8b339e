/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { View, Text, FlatList, Pressable, RefreshControl, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import LottieView from 'lottie-react-native';
import NotFound from '@/src/components/NotFound';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import Filter from '@/src/assets/svgs/Filter';
import { ForumQuestionI, ForumQuestionResultI } from '@/src/networks/question/types';
import NavigationBar from '../../NavigationBar';
import ForumPost from '../ForumPost';
import { transformQuestionToForumPost } from '../ForumPost/utils';
import TopBar from '../TopBar';
import useForumPostList from './useHook';
import ForumActions from '../ForumActions';

const ListFooter = ({ isLoading }: { isLoading: boolean }) => {
  if (!isLoading) return null;
  return (
    <View className="py-4">
      <ActivityIndicator size="small" />
    </View>
  );
};

const ForumPostList = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const {
    questions,
    loading,
    refreshing,
    hasMore,
    isLive,
    handleRefresh,
    handleLoadMore,
    toggleLiveMode,
    handleSelectPost,
  } = useForumPostList();
  const transformedQuestions = questions.map((question) =>
    transformQuestionToForumPost(question as ForumQuestionResultI, question.media),
  );

  const handleEndReached = () => {
    if (hasMore && !loading) {
      handleLoadMore();
    }
  };

  const handleFilter = () => {
    navigation.navigate('ForumFilter');
  };

  return (
    <View className="flex-1">
      <TopBar />
      <ForumActions />
      <View className="p-2 flex-1">
        <FlatList
          data={transformedQuestions}
          keyExtractor={(item) => item.postId}
          renderItem={({ item }) => (
            <Pressable onPress={() => handleSelectPost(item)}>
              <ForumPost post={item} />
            </Pressable>
          )}
          contentContainerStyle={{
            flexGrow: 1,
            backgroundColor: 'white',
            paddingBottom: 20,
          }}
          showsVerticalScrollIndicator={false}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
          onEndReached={handleEndReached}
          onEndReachedThreshold={0.5}
          ListFooterComponentStyle={{
            marginBottom: 50,
          }}
          ListFooterComponent={<ListFooter isLoading={loading} />}
          ListHeaderComponent={
            <>
              {/* <NavigationBar /> */}
              <View className="flex-row items-center justify-between py-1 bg-white">
                <Text className="text-lg font-normal text-black px-3">Latest from community</Text>
                <View className="flex-row items-center px-4">
                  <View
                    className="rounded-full justify-center items-center pr-4"
                    style={{ width: 40, height: 40 }}
                  >
                    <Pressable onPress={toggleLiveMode}>
                      {isLive && (
                        <LottieView
                          source={require('@/src/assets/animations/live.json')}
                          autoPlay
                          loop
                          style={{
                            width: 100,
                            height: 50,
                          }}
                        />
                      )}
                      {!isLive && (
                        <LottieView
                          source={require('@/src/assets/animations/static.json')}
                          style={{
                            width: 100,
                            height: 50,
                          }}
                        />
                      )}
                    </Pressable>
                  </View>
                  <Pressable onPress={handleFilter}>
                    <Filter width={2.5} height={2.5} />
                  </Pressable>
                </View>
              </View>
            </>
          }
          ListEmptyComponent={<NotFound />}
        />
      </View>
    </View>
  );
};

export default ForumPostList;
