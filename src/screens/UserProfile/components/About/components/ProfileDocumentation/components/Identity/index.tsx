import { Text, TouchableOpacity, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import NotFound from '@/src/components/NotFound';
import { navigate } from '@/src/utilities/navigation';
import { BottomTabNavigationI } from '@/src/navigation/types';
import { IdentityDocumentDetails } from '../DocumentDetails';
import { expiresIn } from '../utils';
import { IdentityDocumentI, ProfileIdentityPropsI } from './types';
import { useProfileIdentity } from './useHook';

const ProfileIdentity: React.FC<ProfileIdentityPropsI> = ({ profileId, isUserProfile }) => {
  const { documents, count } = useProfileIdentity(profileId);
  const navigation = useNavigation<BottomTabNavigationI>();

  return (
    <View>
      {documents.map((item: IdentityDocumentI, index: number) => {
        return (
          <IdentityDocumentDetails
            key={index}
            document={item}
            isLast={index === documents.length - 1}
            expiresIn={expiresIn(item.untilDate)}
          />
        );
      })}
      {count > 0 && (
        <TouchableOpacity
          onPress={() =>
            navigation.navigate(isUserProfile ? 'ProfileStack' : 'HomeStack', {
              screen: 'EditDocumentList',
              params: {
                editable: false,
                tab: 'visa',
                profileId: profileId,
              },
            })
          }
        >
          <Text className="text-[#448600] text-base font-medium pt-4">
            {`View all ${count} identity documents`}
          </Text>
        </TouchableOpacity>
      )}
      {count === 0 && (
        <NotFound
          className="pt-10 pb-4"
          titleClassName="font-normal"
          imageStyle={{ height: 120, width: 120 }}
          fullScreen={false}
        />
      )}
    </View>
  );
};

export default ProfileIdentity;
