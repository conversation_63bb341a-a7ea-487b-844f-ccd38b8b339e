import { fetchPostAPI } from '../networks/content/post';
import { fetchScrapbookPost } from '../networks/port/scrapbook';
import { fetchProfileAPI } from '../networks/profile/userProfile';
import { fetchForumQuestionForClientAPI } from '../networks/question/question';
import { DeepLinkCache } from './deeplink-cache';
import { Route, ValidationResult } from './types';

const cache = new DeepLinkCache();

export const validatePost = async (postId: string, type: string): Promise<ValidationResult> => {
  const cacheKey = `post_${postId}`;
  const cached = cache.get(cacheKey);
  if (cached) return { isValid: true, cached: true };

  try {
    const result =
      type === 'USER_POST' ? await fetchPostAPI(postId) : await fetchScrapbookPost(postId);
    if (result) {
      cache.set(cacheKey, result);
      return { isValid: true };
    }
    return { isValid: false };
  } catch {
    return { isValid: false };
  }
};

export const validateProfile = async (profileId: string): Promise<ValidationResult> => {
  const cacheKey = `profile_${profileId}`;
  const cached = cache.get(cacheKey);
  if (cached) return { isValid: true, cached: true };

  try {
    const response = await fetchProfileAPI(profileId);
    cache.set(cacheKey, response);
    return { isValid: true };
  } catch {
    return { isValid: false };
  }
};

export const validateForumPost = async (postId: string): Promise<ValidationResult> => {
  const cacheKey = `forum_${postId}`;
  const cached = cache.get(cacheKey);
  if (cached) return { isValid: true, cached: true };

  try {
    const result = await fetchForumQuestionForClientAPI(postId);
    if (result) {
      cache.set(cacheKey, result);
      return { isValid: true };
    }
    return { isValid: false };
  } catch {
    return { isValid: false };
  }
};

export const validateRoute = async (
  route: Route,
  params: Record<string, string>,
): Promise<ValidationResult> => {
  if (route === 'post') return validatePost(params.postId, params.type);
  if (route === 'profile') return validateProfile(params.profileId);
  if (route === 'forum') return validateForumPost(params.postId);
  return { isValid: false };
};
