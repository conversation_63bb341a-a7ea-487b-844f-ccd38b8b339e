import * as React from "react"
import { RFPercentage } from "react-native-responsive-fontsize"
import Svg, { Path } from "react-native-svg"
import { OutlinedIconPropsI } from "./types"

const ExploreContained: React.FC<OutlinedIconPropsI> = ({
    width = 2,
    height = 2,
    fill = '#448600',
    color,
    disabled,
    ...props
}) => {
    const mainFill = color || fill;
    const opacity = disabled ? 0.5 : 1;
    return (
        <Svg
            width={RFPercentage(width)}
            height={RFPercentage(height)}
            viewBox="0 0 24 24"
            fill="none"
            {...props}
        >
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2zm5.984 5.39c.215-.745-.487-1.446-1.233-1.232l-.102.035L9.58 9.02l-.106.05a1 1 0 00-.39.382l-.062.125-2.828 7.071-.035.102c-.214.746.487 1.448 1.233 1.233l.102-.035 7.07-2.828.106-.05a1 1 0 00.391-.381l.06-.126 2.83-7.071.034-.102zM12 10a2 2 0 110 4 2 2 0 010-4z"
                fill={fill ?? "#000"}
            />
        </Svg>
    )
}

export default ExploreContained
