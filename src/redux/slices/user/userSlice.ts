/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import AppError from '@/src/errors/networks/AppError';
import { deactivateAccountAPI, deleteAccountAPI } from '@/src/networks/auth/deleteAccount';
import { verifyOTPForEmailVerificationAPI } from '@/src/networks/auth/email';
import {
  sendOTPForPasswordResetAPI,
  verifyOTPForPasswordResetAPI,
  resetPasswordAPI,
} from '@/src/networks/auth/forgot';
import { loginAPI } from '@/src/networks/auth/login';
import { logoutAPI } from '@/src/networks/auth/logout';
import { registerAPI } from '@/src/networks/auth/register';
import { AuthTypeI } from '@/src/networks/auth/types';
import { updateUsernameAPI } from '@/src/networks/profile/username';
import { fetchProfileAPI, editUserProfileAPI } from '@/src/networks/profile/userProfile';
import { SearchResultI } from '../entitysearch/types';
import {
  UserState,
  FetchProfileResultI,
  LoginResponse,
  RegisterResponse,
  ResetPasswordResponse,
} from './types';

const initialState: UserState = {
  username: '',
  email: '',
  profileId: '',
  fullName: '',
  gender: '',
  avatar: null,
  country: undefined,
  organisation: undefined,
  designation: undefined,
  isUsernameSaved: false,
  isEmailVerified: false,
  isPersonalDetailsSaved: false,
  isWorkDetailsSaved: false,
  isAuthenticated: false,
  isPrivacyPolicyAccepted: false,
  token: '',
  jwtToken: '',
  loading: false,
  error: null,
  pendingVerificationEmail: undefined,
  registrationProfileId: undefined,
  forgotPasswordEmail: undefined,
  isOTPVerified: false,
  previousStatus: 'ACTIVE',
};

export const registerAsync = createAsyncThunk<
  RegisterResponse,
  { email: string; password: string; confirmPassword: string; isPPAndTNCAccepted: boolean },
  { rejectValue: string }
>(
  'user/register',
  async ({ email, password, confirmPassword, isPPAndTNCAccepted }, { rejectWithValue }) => {
    try {
      const registerResult = await registerAPI({
        type: 'EMAIL_PASSWORD',
        email,
        password,
        confirmPassword,
        isPPAndTNCAccepted,
      });
      return registerResult;
    } catch (error: unknown) {
      return rejectWithValue((error as Error).message);
    }
  },
);

export const verifyEmailOTPAsync = createAsyncThunk<
  void,
  { otp: string; profileId: string },
  { rejectValue: string }
>('user/verifyEmailOTP', async ({ otp, profileId }, { rejectWithValue }) => {
  try {
    await verifyOTPForEmailVerificationAPI({ otp, profileId });
  } catch (error: unknown) {
    return rejectWithValue((error as Error).message);
  }
});

export const signInAsync = createAsyncThunk<
  LoginResponse,
  { email: string; password: string; deviceToken?: string },
  { rejectValue: string }
>('user/signIn', async ({ email, password, deviceToken }, { rejectWithValue }) => {
  try {
    const loginResult = await loginAPI({
      type: 'EMAIL_PASSWORD',
      email,
      password,
      deviceToken,
    });
    if (!loginResult?.token) {
      throw new AppError('Invalid email or password');
    }
    return loginResult;
  } catch (error: unknown) {
    return rejectWithValue((error as Error).message);
  }
});

export const externalSignInAsync = createAsyncThunk<
  LoginResponse,
  { externalToken: string; deviceToken?: string; type: AuthTypeI },
  { rejectValue: string }
>('user/externalSignIn', async ({ externalToken, deviceToken, type }, { rejectWithValue }) => {
  try {
    const loginResult = await loginAPI({
      type,
      externalToken,
      ...(deviceToken ? { deviceToken } : {}),
    });
    if (!loginResult?.token) {
      throw new AppError(`${type} sign-in failed`);
    }
    if (!loginResult.email) {
      throw new AppError('Email not provided in login response');
    }
    return loginResult;
  } catch (error: unknown) {
    return rejectWithValue((error as Error).message);
  }
});

export const signOutAsync = createAsyncThunk('user/logout', async (_, { rejectWithValue }) => {
  try {
    await logoutAPI();
  } catch (error: unknown) {
    return rejectWithValue((error as Error).message);
  }
});

export const deleteAccountAsync = createAsyncThunk(
  'profile/delete',
  async (_, { rejectWithValue }) => {
    try {
      await deleteAccountAPI();
    } catch (error: unknown) {
      return rejectWithValue((error as Error).message);
    }
  },
);

export const deactivateAccountAsync = createAsyncThunk(
  'profile/deactivate',
  async (_, { rejectWithValue }) => {
    try {
      await deactivateAccountAPI();
    } catch (error: unknown) {
      return rejectWithValue((error as Error).message);
    }
  },
);

export const sendOTPForPasswordResetAsync = createAsyncThunk<
  void,
  { email: string },
  { rejectValue: string }
>('user/sendOTPForPasswordReset', async ({ email }, { rejectWithValue }) => {
  try {
    await sendOTPForPasswordResetAPI({ email });
  } catch (error: unknown) {
    return rejectWithValue((error as Error).message);
  }
});

export const verifyOTPForPasswordResetAsync = createAsyncThunk<
  void,
  { email: string; otp: string },
  { rejectValue: string }
>('user/verifyOTPForPasswordReset', async ({ email, otp }, { rejectWithValue }) => {
  try {
    await verifyOTPForPasswordResetAPI({ email, otp });
  } catch (error: unknown) {
    return rejectWithValue((error as Error).message);
  }
});

export const resetPasswordAsync = createAsyncThunk<
  ResetPasswordResponse,
  { email: string; newPassword: string },
  { rejectValue: string }
>('user/resetPassword', async ({ email, newPassword }, { rejectWithValue }) => {
  try {
    const result = await resetPasswordAPI({ email, newPassword });
    return result;
  } catch (error: unknown) {
    return rejectWithValue((error as Error).message);
  }
});

export const saveUsernameAsync = createAsyncThunk<
  { username: string },
  { username: string },
  { rejectValue: string }
>('user/saveUsername', async ({ username }, { rejectWithValue }) => {
  try {
    await updateUsernameAPI({ username });
    return { username };
  } catch (error) {
    return rejectWithValue('Failed to save username');
  }
});

export const setProfileAsync = createAsyncThunk<
  {
    fullName: string;
    gender: string;
    country?: SearchResultI;
    organisation?: SearchResultI;
    designation?: SearchResultI;
  },
  {
    fullName: string;
    gender: string;
    country?: SearchResultI;
    organisation?: SearchResultI;
    designation?: SearchResultI;
  },
  { rejectValue: string }
>('user/setProfile', async (profileData, { rejectWithValue }) => {
  try {
    return profileData;
  } catch (error) {
    return rejectWithValue('Failed to set profile');
  }
});

export const fetchAndSaveUserProfile = createAsyncThunk<
  FetchProfileResultI,
  { id: string },
  { rejectValue: string }
>('user/fetchProfile', async ({ id }, { rejectWithValue }) => {
  try {
    const response = await fetchProfileAPI(id);
    return response;
  } catch (error) {
    return rejectWithValue('Failed to fetch profile');
  }
});

export const updateUserProfileAsync = createAsyncThunk<
  { name?: string; designation?: SearchResultI; entity?: SearchResultI; avatar: string | null },
  { name?: string; designation?: SearchResultI; entity?: SearchResultI; avatar: string | null },
  { rejectValue: string }
>('user/updateUserProfile', async (profileData, { rejectWithValue }) => {
  try {
    await editUserProfileAPI(profileData);
    return profileData;
  } catch (error) {
    return rejectWithValue('Failed to update profile');
  }
});

const updateAuthenticationStatus = (state: UserState) => {
  state.isAuthenticated =
    !!state.token &&
    !!state.username &&
    !!state.email &&
    !!state.profileId &&
    state.isUsernameSaved &&
    state.isPersonalDetailsSaved &&
    state.isWorkDetailsSaved &&
    state.isPrivacyPolicyAccepted;
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    resetUserState: () => initialState,
    updateWorkDetail: (state) => {
      state.isWorkDetailsSaved = true;
      updateAuthenticationStatus(state);
    },
    updateProfileDetail: (state) => {
      state.isPersonalDetailsSaved = true;
      updateAuthenticationStatus(state);
    },
    updatePrivacyPolicyAcceptance: (state) => {
      state.isPrivacyPolicyAccepted = true;
      updateAuthenticationStatus(state);
    },
    updateUserProfile: (
      state,
      action: PayloadAction<{
        name?: string;
        designation?: SearchResultI;
        entity?: SearchResultI;
        avatar: string | null;
      }>,
    ) => {
      const { name, designation, entity, avatar } = action.payload;
      if (name !== undefined) state.fullName = name;
      if (designation !== undefined) state.designation = designation;
      if (entity !== undefined) state.organisation = entity;
      state.avatar = avatar;
    },
    clearError: (state) => {
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    updateAvatar: (state, action: PayloadAction<string | null>) => {
      state.avatar = action.payload;
    },
    verifyEmail: (state) => {
      state.isEmailVerified = true;
      updateAuthenticationStatus(state);
    },
    setPendingVerificationEmail: (state, action: PayloadAction<string>) => {
      state.pendingVerificationEmail = action.payload;
    },
    clearPendingVerification: (state) => {
      state.pendingVerificationEmail = undefined;
      state.registrationProfileId = undefined;
    },
    setForgotPasswordEmail: (state, action: PayloadAction<string>) => {
      state.forgotPasswordEmail = action.payload;
    },
    clearForgotPasswordState: (state) => {
      state.forgotPasswordEmail = undefined;
      state.isOTPVerified = false;
    },
    clearPreviousStatus: (state) => {
      state.previousStatus = undefined;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(registerAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(registerAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.registrationProfileId = action.payload.profileId;
        state.error = null;
      })
      .addCase(registerAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Registration failed';
      })
      .addCase(verifyEmailOTPAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyEmailOTPAsync.fulfilled, (state) => {
        state.loading = false;
        state.isEmailVerified = true;
        state.pendingVerificationEmail = undefined;
        state.registrationProfileId = undefined;
        state.error = null;
        updateAuthenticationStatus(state);
      })
      .addCase(verifyEmailOTPAsync.rejected, (state, action) => {
        state.loading = false;
        state.isEmailVerified = false;
        state.error = action.payload || 'OTP verification failed';
      })
      .addCase(signInAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(signInAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.email = action.payload.email;
        state.username = action.payload.username;
        state.profileId = action.payload.profileId;
        state.avatar = action.payload.avatar;
        state.isEmailVerified = action.payload.isEmailVerified;
        state.isUsernameSaved = action.payload.isUsernameSaved;
        state.isPersonalDetailsSaved = action.payload.isPersonalDetailsSaved;
        state.isWorkDetailsSaved = action.payload.isWorkDetailsSaved;
        state.isPrivacyPolicyAccepted = action.payload.isPrivacyPolicyAccepted;
        state.token = action.payload.token;
        state.jwtToken = action.payload.jwtToken;
        state.previousStatus = action.payload.previousStatus;
        updateAuthenticationStatus(state);
      })
      .addCase(signInAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Sign in failed';
      })
      .addCase(externalSignInAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(externalSignInAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.email = action.payload.email;
        state.username = action.payload.username;
        state.profileId = action.payload.profileId;
        state.avatar = action.payload.avatar;
        state.isEmailVerified = action.payload.isEmailVerified;
        state.isUsernameSaved = action.payload.isUsernameSaved;
        state.isPersonalDetailsSaved = action.payload.isPersonalDetailsSaved;
        state.isWorkDetailsSaved = action.payload.isWorkDetailsSaved;
        state.isPrivacyPolicyAccepted = action.payload.isPrivacyPolicyAccepted;
        state.token = action.payload.token;
        state.jwtToken = action.payload.jwtToken;
        state.previousStatus = action.payload.previousStatus;
        updateAuthenticationStatus(state);
      })
      .addCase(externalSignInAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Google sign in failed';
      })
      .addCase(signOutAsync.fulfilled, (state) => {
        Object.assign(state, initialState);
      })
      .addCase(deleteAccountAsync.fulfilled, (state) => {
        Object.assign(state, initialState);
      })
      .addCase(deactivateAccountAsync.fulfilled, (state) => {
        Object.assign(state, initialState);
      })
      .addCase(sendOTPForPasswordResetAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(sendOTPForPasswordResetAsync.fulfilled, (state) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(sendOTPForPasswordResetAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to send OTP';
      })
      .addCase(verifyOTPForPasswordResetAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyOTPForPasswordResetAsync.fulfilled, (state) => {
        state.loading = false;
        state.isOTPVerified = true;
        state.error = null;
      })
      .addCase(verifyOTPForPasswordResetAsync.rejected, (state, action) => {
        state.loading = false;
        state.isOTPVerified = false;
        state.error = action.payload || 'OTP verification failed';
      })
      .addCase(resetPasswordAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(resetPasswordAsync.fulfilled, (state) => {
        state.loading = false;
        state.forgotPasswordEmail = undefined;
        state.isOTPVerified = false;
        state.error = null;
      })
      .addCase(resetPasswordAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Password reset failed';
      })
      .addCase(saveUsernameAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(saveUsernameAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.username = action.payload.username;
        state.isUsernameSaved = true;
        updateAuthenticationStatus(state);
      })
      .addCase(saveUsernameAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to save username';
      })
      .addCase(setProfileAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(setProfileAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.fullName = action.payload.fullName;
        state.gender = action.payload.gender;
        state.country = action.payload.country;
        state.organisation = action.payload.organisation;
        state.designation = action.payload.designation;
        updateAuthenticationStatus(state);
      })
      .addCase(setProfileAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to set profile';
      })
      .addCase(fetchAndSaveUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAndSaveUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.fullName = action.payload.name;
        state.avatar = action.payload.avatar;
        state.country = undefined;

        state.designation = action.payload.designation
          ? {
              id: action.payload.designation.id,
              name: action.payload.designation.name,
              dataType: action.payload.designation.dataType,
            }
          : undefined;

        state.organisation = action.payload.entity
          ? {
              id: action.payload.entity.id,
              name: action.payload.entity.name,
              dataType: action.payload.entity.dataType,
            }
          : undefined;

        updateAuthenticationStatus(state);
      })
      .addCase(fetchAndSaveUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch profile';
      })
      .addCase(updateUserProfileAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUserProfileAsync.fulfilled, (state, action) => {
        state.loading = false;
        const { name, designation, entity } = action.payload;
        if (name !== undefined) state.fullName = name;
        if (designation !== undefined) state.designation = designation;
        if (entity !== undefined) state.organisation = entity;
      })
      .addCase(updateUserProfileAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to update profile';
      });
  },
});

export const {
  resetUserState,
  updateWorkDetail,
  updateProfileDetail,
  updateUserProfile,
  clearError,
  setLoading,
  updateAvatar,
  verifyEmail,
  setPendingVerificationEmail,
  clearPendingVerification,
  setForgotPasswordEmail,
  clearForgotPasswordState,
  updatePrivacyPolicyAcceptance,
  clearPreviousStatus,
} = userSlice.actions;

export default userSlice.reducer;
