/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createSlice } from '@reduxjs/toolkit';
import { ForumSearchStateI } from './types';

const initialState: ForumSearchStateI = {
  recentSearches: [],
};

const forumSearchSlice = createSlice({
  name: 'forumSearch',
  initialState,
  reducers: {
    addForumSearch: (state, action) => {
      const { category, searchText } = action.payload;
      if (!searchText) return;
      
      state.recentSearches = state.recentSearches.filter(
        (item) => !(item.searchText === searchText && item.category === category),
      );
      
      state.recentSearches.unshift({ category, searchText });
      
      if (state.recentSearches.length > 5) {
        state.recentSearches.pop();
      }
    },
    clearForumSearches: (state) => {
      state.recentSearches = [];
    },
  },
});

export const { addForumSearch, clearForumSearches } = forumSearchSlice.actions;
export default forumSearchSlice.reducer;
